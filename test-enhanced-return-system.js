const axios = require('axios');
require('dotenv').config();

class EnhancedReturnSystemTester {
  constructor() {
    this.baseUrl = process.env.WEBHOOK_BASE_URL || 'http://localhost:3000';
  }

  /**
   * Test the enhanced workflow
   */
  async testEnhancedWorkflow() {
    console.log('🧪 Testing Enhanced Return Processing Workflow...\n');

    try {
      const response = await axios.get(`${this.baseUrl}/test/enhanced-workflow`);
      const result = response.data;

      console.log('📊 Enhanced Workflow Test Results:');
      console.log(`   Service: ${result.service}`);
      console.log(`   Overall Success: ${result.success ? '✅' : '❌'}`);
      console.log(`   Timestamp: ${result.timestamp}\n`);

      console.log('🔍 Individual Test Results:');
      for (const [testName, passed] of Object.entries(result.tests)) {
        console.log(`   ${testName}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
      }

      if (result.errors && result.errors.length > 0) {
        console.log('\n❌ Errors Found:');
        result.errors.forEach((error, index) => {
          console.log(`   ${index + 1}. ${error}`);
        });
      }

      return result.success;
    } catch (error) {
      console.log('❌ Enhanced workflow test failed:', error.message);
      return false;
    }
  }

  /**
   * Test Return Prime API connection
   */
  async testReturnPrimeAPI() {
    console.log('🔗 Testing Return Prime API Connection...\n');

    try {
      const response = await axios.get(`${this.baseUrl}/test/return-prime`);
      const result = response.data;

      console.log('📊 Return Prime API Test Results:');
      console.log(`   Service: ${result.service}`);
      console.log(`   Connected: ${result.connected ? '✅' : '❌'}`);
      console.log(`   Timestamp: ${result.timestamp}\n`);

      if (!result.connected && result.error) {
        console.log(`❌ Connection Error: ${result.error}`);
      }

      return result.connected;
    } catch (error) {
      console.log('❌ Return Prime API test failed:', error.message);
      return false;
    }
  }

  /**
   * Test webhook processing with sample data
   */
  async testWebhookProcessing() {
    console.log('📨 Testing Webhook Processing...\n');

    // Sample Return Prime webhook payload for refund
    const samplePayload = {
      request: {
        id: "test_return_" + Date.now(),
        status: "inspected",
        created_at: new Date().toISOString(),
        request_number: "RET-TEST-001",
        request_type: "return",
        customer: {
          id: 12345,
          name: "Test Customer",
          email: "<EMAIL>",
          phone: "******-0123"
        },
        order: {
          id: 67890,
          name: "#TEST-001",
          created_at: new Date().toISOString(),
          fulfillments: []
        },
        line_items: [
          {
            id: 11111,
            quantity: 1,
            reason: "Defective",
            original_product: {
              product_id: 9698464760088, // Vessel product from sample
              variant_id: 49924238278936,
              sku: "036265ec-6c5a-4273-9e75-d15e7f50823b",
              title: "Vessel - Air [Emerald]",
              price: 20,
              product_deleted: false,
              variant_deleted: false
            },
            refund: {
              status: "refunded",
              requested_mode: "refund",
              actual_mode: "refund",
              refunded_at: new Date().toISOString()
            },
            presentment_price: {
              actual_amount: 20,
              currency: "USD",
              return_quantity: 1,
              shipping_amount: 0,
              total_discount: 0,
              total_tax: 0
            }
          }
        ],
        approved: {
          status: true,
          created_at: new Date().toISOString()
        },
        rejected: {
          status: false
        },
        received: {
          status: true,
          created_at: new Date().toISOString()
        },
        inspected: {
          status: true,
          created_at: new Date().toISOString()
        },
        archived: {
          status: false
        },
        manual_request: false,
        smart_exchange: false
      }
    };

    const headers = {
      'x-rp-topic': 'request/refunded',
      'x-rp-store': 'tgm1vh-mn.myshopify.com',
      'content-type': 'application/json'
    };

    try {
      console.log('📤 Sending test webhook...');
      const response = await axios.post(
        `${this.baseUrl}/webhook/return-prime`,
        samplePayload,
        { headers }
      );

      const result = response.data;

      console.log('📊 Webhook Processing Results:');
      console.log(`   Success: ${result.success ? '✅' : '❌'}`);
      console.log(`   Message: ${result.message}`);
      console.log(`   Processed Items: ${result.processedItems || 0}`);

      if (result.errors && result.errors.length > 0) {
        console.log('\n⚠️  Processing Errors:');
        result.errors.forEach((error, index) => {
          console.log(`   ${index + 1}. [${error.code}] ${error.message}`);
        });
      }

      return result.success;
    } catch (error) {
      console.log('❌ Webhook processing test failed:', error.message);
      if (error.response?.data) {
        console.log('   Response:', JSON.stringify(error.response.data, null, 2));
      }
      return false;
    }
  }

  /**
   * Test vendor database functionality
   */
  async testVendorDatabase() {
    console.log('🗄️  Testing Vendor Database...\n');

    try {
      // This would typically be done through an API endpoint
      // For now, we'll check if the vendor database file exists and has data
      const fs = require('fs');
      const path = require('path');
      
      const dbPath = path.join(process.cwd(), 'vendor-database.json');
      
      if (!fs.existsSync(dbPath)) {
        console.log('❌ Vendor database file not found');
        return false;
      }

      const dbContent = fs.readFileSync(dbPath, 'utf8');
      const database = JSON.parse(dbContent);

      console.log('📊 Vendor Database Status:');
      console.log(`   Total Vendors: ${database.totalVendors || 0}`);
      console.log(`   Last Updated: ${database.lastUpdated || 'Unknown'}`);
      
      const verifiedVendors = Object.values(database.vendors || {}).filter(v => v.verified).length;
      console.log(`   Verified Vendors: ${verifiedVendors}`);

      // Check for key vendors
      const keyVendors = ['Canna River', 'GRAV®', 'Vessel'];
      console.log('\n🔍 Key Vendor Status:');
      
      for (const vendorName of keyVendors) {
        const vendor = database.vendors?.[vendorName];
        if (vendor) {
          console.log(`   ${vendorName}: ✅ Found (${vendor.verified ? 'Verified' : 'Unverified'})`);
          console.log(`     Email: ${vendor.contact?.email || 'Not set'}`);
          console.log(`     Priority: ${vendor.priority || 'Not set'}`);
        } else {
          console.log(`   ${vendorName}: ❌ Not found`);
        }
      }

      return database.totalVendors > 0;
    } catch (error) {
      console.log('❌ Vendor database test failed:', error.message);
      return false;
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🚀 Starting Enhanced Return System Tests...\n');
    console.log('='.repeat(60) + '\n');

    const results = {
      enhancedWorkflow: false,
      returnPrimeAPI: false,
      webhookProcessing: false,
      vendorDatabase: false
    };

    // Test 1: Enhanced Workflow
    console.log('TEST 1: Enhanced Workflow');
    console.log('-'.repeat(30));
    results.enhancedWorkflow = await this.testEnhancedWorkflow();
    console.log('\n' + '='.repeat(60) + '\n');

    // Test 2: Return Prime API
    console.log('TEST 2: Return Prime API');
    console.log('-'.repeat(30));
    results.returnPrimeAPI = await this.testReturnPrimeAPI();
    console.log('\n' + '='.repeat(60) + '\n');

    // Test 3: Vendor Database
    console.log('TEST 3: Vendor Database');
    console.log('-'.repeat(30));
    results.vendorDatabase = await this.testVendorDatabase();
    console.log('\n' + '='.repeat(60) + '\n');

    // Test 4: Webhook Processing
    console.log('TEST 4: Webhook Processing');
    console.log('-'.repeat(30));
    results.webhookProcessing = await this.testWebhookProcessing();
    console.log('\n' + '='.repeat(60) + '\n');

    // Summary
    console.log('📊 TEST SUMMARY');
    console.log('-'.repeat(30));
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`Overall Result: ${passedTests === totalTests ? '✅ ALL TESTS PASSED' : '⚠️  SOME TESTS FAILED'}`);
    console.log(`Tests Passed: ${passedTests}/${totalTests}\n`);

    console.log('Individual Results:');
    for (const [testName, passed] of Object.entries(results)) {
      const displayName = testName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      console.log(`   ${displayName}: ${passed ? '✅ PASS' : '❌ FAIL'}`);
    }

    console.log('\n🎯 Next Steps:');
    if (passedTests === totalTests) {
      console.log('   ✅ System is ready for production use!');
      console.log('   📧 Supplier notifications will be sent automatically for refund requests');
      console.log('   🔄 Return Prime webhooks will be processed and routed to correct suppliers');
    } else {
      console.log('   ⚠️  Please address the failed tests before using in production');
      if (!results.enhancedWorkflow) {
        console.log('   🔧 Check service configurations and dependencies');
      }
      if (!results.returnPrimeAPI) {
        console.log('   🔑 Verify Return Prime API credentials and connectivity');
      }
      if (!results.vendorDatabase) {
        console.log('   📋 Ensure vendor database is properly populated');
      }
      if (!results.webhookProcessing) {
        console.log('   🔗 Check webhook endpoint and processing logic');
      }
    }

    console.log('\n🏁 Testing completed!');
    return passedTests === totalTests;
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new EnhancedReturnSystemTester();
  tester.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('💥 Test runner failed:', error.message);
    process.exit(1);
  });
}

module.exports = EnhancedReturnSystemTester;
