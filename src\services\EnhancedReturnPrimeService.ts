import { ReturnPrimeWebhookPayload, TransformedReturnPayload, ReturnItem } from '../types';
import { ShopifyService } from './ShopifyService';
import { VendorDatabaseService, VendorInfo } from './VendorDatabaseService';
import { SupplierNotificationService, SupplierNotificationData } from './SupplierNotificationService';
import { CustomerNotificationService, CustomerNotificationData } from './CustomerNotificationService';
import { transformReturnPrimePayload, getEventTypeFromHeaders } from '../utils/transform';
import logger from '../utils/logger';

export interface ProcessedReturn {
  returnId: string;
  orderId: string;
  customerEmail: string;
  customerName?: string;
  items: Array<{
    item: ReturnItem;
    vendor?: string;
    vendorInfo?: VendorInfo;
    notificationSent: boolean;
    error?: string;
  }>;
  totalItems: number;
  processedItems: number;
  notificationsSent: number;
  customerNotified: boolean;
  errors: string[];
}

export class EnhancedReturnPrimeService {
  private shopifyService: ShopifyService;
  private vendorDatabase: VendorDatabaseService;
  private notificationService: SupplierNotificationService;
  private customerNotificationService: CustomerNotificationService;

  constructor() {
    this.shopifyService = new ShopifyService();
    this.vendorDatabase = new VendorDatabaseService();
    this.notificationService = new SupplierNotificationService();
    this.customerNotificationService = new CustomerNotificationService();
  }

  /**
   * Process Return Prime webhook for refund requests
   */
  public async processRefundWebhook(
    payload: ReturnPrimeWebhookPayload,
    headers: Record<string, any>
  ): Promise<{
    success: boolean;
    processedReturn?: ProcessedReturn;
    error?: string;
  }> {
    try {
      // Transform payload to internal format
      const transformedPayload = transformReturnPrimePayload(payload);
      const eventType = getEventTypeFromHeaders(headers);

      logger.info('Processing Return Prime refund webhook', {
        returnId: transformedPayload.return_id,
        eventType,
        itemCount: transformedPayload.items.length
      });

      // Only process refund events
      if (!eventType.includes('refund')) {
        logger.info('Skipping non-refund event', {
          returnId: transformedPayload.return_id,
          eventType
        });
        return {
          success: true,
          processedReturn: {
            returnId: transformedPayload.return_id,
            orderId: transformedPayload.order_id,
            customerEmail: transformedPayload.customer_email,
            customerName: transformedPayload.customer_name,
            items: [],
            totalItems: 0,
            processedItems: 0,
            notificationsSent: 0,
            customerNotified: false,
            errors: ['Skipped: Not a refund event']
          }
        };
      }

      // Process the return
      const processedReturn = await this.processReturn(transformedPayload);

      return {
        success: true,
        processedReturn
      };
    } catch (error: any) {
      logger.error('Failed to process Return Prime refund webhook', {
        error: error.message,
        payload: JSON.stringify(payload, null, 2)
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process a return request and notify suppliers
   */
  private async processReturn(payload: TransformedReturnPayload): Promise<ProcessedReturn> {
    const processedReturn: ProcessedReturn = {
      returnId: payload.return_id,
      orderId: payload.order_id,
      customerEmail: payload.customer_email,
      customerName: payload.customer_name,
      items: [],
      totalItems: payload.items.length,
      processedItems: 0,
      notificationsSent: 0,
      customerNotified: false,
      errors: []
    };

    try {
      // Process each item to find vendor information
      for (const item of payload.items) {
        const processedItem = {
          item,
          vendor: undefined as string | undefined,
          vendorInfo: undefined as VendorInfo | undefined,
          notificationSent: false,
          error: undefined as string | undefined
        };

        try {
          // Get vendor information from Shopify
          let vendorResult;
          
          if (item.product_id) {
            vendorResult = await this.shopifyService.getVendorForProduct(item.product_id);
          } else if (item.variant_id) {
            vendorResult = await this.shopifyService.getVendorForVariant(item.variant_id);
          }

          if (vendorResult?.success && vendorResult.vendor) {
            processedItem.vendor = vendorResult.vendor;
            processedItem.vendorInfo = vendorResult.vendorInfo;
            
            logger.debug('Found vendor for item', {
              sku: item.sku,
              vendor: vendorResult.vendor,
              hasVendorInfo: !!vendorResult.vendorInfo
            });
          } else {
            processedItem.error = 'Vendor not found in Shopify';
            logger.warn('No vendor found for item', {
              sku: item.sku,
              productId: item.product_id,
              variantId: item.variant_id
            });
          }

          processedReturn.processedItems++;
        } catch (error: any) {
          processedItem.error = `Failed to get vendor: ${error.message}`;
          logger.error('Error processing item', {
            sku: item.sku,
            error: error.message
          });
        }

        processedReturn.items.push(processedItem);
      }

      // Group items by vendor for notification
      const vendorGroups = this.groupItemsByVendor(processedReturn.items);

      // Send notifications to each vendor
      for (const [vendorName, vendorData] of Object.entries(vendorGroups)) {
        try {
          if (!vendorData.vendorInfo) {
            logger.warn('No vendor info available for notification', {
              vendor: vendorName,
              itemCount: vendorData.items.length
            });
            
            // Mark items as failed
            vendorData.items.forEach(processedItem => {
              processedItem.error = 'No vendor contact information available';
            });
            
            processedReturn.errors.push(`No contact info for vendor: ${vendorName}`);
            continue;
          }

          // Create notification data
          const notificationData: SupplierNotificationData = {
            returnId: payload.return_id,
            orderId: payload.order_id,
            customerEmail: payload.customer_email,
            customerName: payload.customer_name,
            items: vendorData.items.map(pi => pi.item),
            totalRefundAmount: payload.refund_amount,
            returnDate: payload.created_at,
            notes: payload.notes
          };

          // Send notification
          const notificationResult = await this.notificationService.notifySupplier(
            vendorData.vendorInfo,
            notificationData
          );

          if (notificationResult.success) {
            // Mark items as notified
            vendorData.items.forEach(processedItem => {
              processedItem.notificationSent = true;
            });
            
            processedReturn.notificationsSent++;
            
            logger.info('Supplier notification sent successfully', {
              vendor: vendorName,
              email: vendorData.vendorInfo.contact.email,
              itemCount: vendorData.items.length,
              messageId: notificationResult.messageId
            });
          } else {
            // Mark items as failed
            vendorData.items.forEach(processedItem => {
              processedItem.error = `Notification failed: ${notificationResult.error}`;
            });
            
            processedReturn.errors.push(`Failed to notify ${vendorName}: ${notificationResult.error}`);
            
            logger.error('Failed to send supplier notification', {
              vendor: vendorName,
              error: notificationResult.error
            });
          }
        } catch (error: any) {
          vendorData.items.forEach(processedItem => {
            processedItem.error = `Notification error: ${error.message}`;
          });
          
          processedReturn.errors.push(`Error notifying ${vendorName}: ${error.message}`);
          
          logger.error('Error sending vendor notification', {
            vendor: vendorName,
            error: error.message
          });
        }
      }

      // Send customer notification after processing suppliers
      try {
        const customerNotificationData: CustomerNotificationData = {
          returnId: payload.return_id,
          orderId: payload.order_id,
          orderNumber: payload.order_number,
          customerEmail: payload.customer_email,
          customerName: payload.customer_name,
          items: payload.items,
          requestType: 'return', // Default to return, could be enhanced to detect type
          submittedDate: payload.created_at,
          estimatedProcessingTime: '3-5 business days',
          notes: payload.notes
        };

        const customerNotificationResult = await this.customerNotificationService.notifyCustomer(
          customerNotificationData
        );

        if (customerNotificationResult.success) {
          processedReturn.customerNotified = true;
          logger.info('Customer notification sent successfully', {
            customerEmail: payload.customer_email,
            returnId: payload.return_id,
            messageId: customerNotificationResult.messageId
          });
        } else {
          processedReturn.errors.push(`Failed to notify customer: ${customerNotificationResult.error}`);
          logger.error('Failed to send customer notification', {
            customerEmail: payload.customer_email,
            returnId: payload.return_id,
            error: customerNotificationResult.error
          });
        }
      } catch (error: any) {
        processedReturn.errors.push(`Customer notification error: ${error.message}`);
        logger.error('Error sending customer notification', {
          customerEmail: payload.customer_email,
          returnId: payload.return_id,
          error: error.message
        });
      }

      logger.info('Return processing completed', {
        returnId: payload.return_id,
        totalItems: processedReturn.totalItems,
        processedItems: processedReturn.processedItems,
        notificationsSent: processedReturn.notificationsSent,
        customerNotified: processedReturn.customerNotified,
        errors: processedReturn.errors.length
      });

      return processedReturn;
    } catch (error: any) {
      logger.error('Error processing return', {
        returnId: payload.return_id,
        error: error.message
      });

      processedReturn.errors.push(`Processing error: ${error.message}`);
      return processedReturn;
    }
  }

  /**
   * Group processed items by vendor
   */
  private groupItemsByVendor(
    processedItems: ProcessedReturn['items']
  ): Record<string, {
    vendorInfo?: VendorInfo;
    items: ProcessedReturn['items'];
  }> {
    const groups: Record<string, {
      vendorInfo?: VendorInfo;
      items: ProcessedReturn['items'];
    }> = {};

    for (const processedItem of processedItems) {
      if (processedItem.vendor) {
        if (!groups[processedItem.vendor]) {
          groups[processedItem.vendor] = {
            vendorInfo: processedItem.vendorInfo,
            items: []
          };
        }
        groups[processedItem.vendor].items.push(processedItem);
      }
    }

    return groups;
  }

  /**
   * Get return processing statistics
   */
  public async getProcessingStats(dateFrom?: string, dateTo?: string): Promise<{
    totalReturns: number;
    processedReturns: number;
    notificationsSent: number;
    topVendors: Array<{ vendor: string; returnCount: number }>;
  }> {
    // This would typically query a database of processed returns
    // For now, return basic stats from vendor database
    try {
      const stats = await this.vendorDatabase.getDatabaseStats();
      
      return {
        totalReturns: 0, // Would come from return processing logs
        processedReturns: 0, // Would come from return processing logs
        notificationsSent: 0, // Would come from notification logs
        topVendors: [] // Would come from return processing logs
      };
    } catch (error: any) {
      logger.error('Failed to get processing stats', { error: error.message });
      return {
        totalReturns: 0,
        processedReturns: 0,
        notificationsSent: 0,
        topVendors: []
      };
    }
  }

  /**
   * Test the complete workflow
   */
  public async testWorkflow(): Promise<{
    success: boolean;
    tests: Record<string, boolean>;
    errors: string[];
  }> {
    const tests: Record<string, boolean> = {};
    const errors: string[] = [];

    try {
      // Test Shopify connection
      tests.shopifyConnection = await this.shopifyService.testConnection();
      if (!tests.shopifyConnection) {
        errors.push('Shopify API connection failed');
      }

      // Test vendor database
      try {
        const stats = await this.vendorDatabase.getDatabaseStats();
        tests.vendorDatabase = stats.totalVendors > 0;
        if (!tests.vendorDatabase) {
          errors.push('Vendor database is empty');
        }
      } catch (error: any) {
        tests.vendorDatabase = false;
        errors.push(`Vendor database error: ${error.message}`);
      }

      // Test supplier email service
      tests.supplierEmailService = await this.notificationService.testEmailConnection();
      if (!tests.supplierEmailService) {
        errors.push('Supplier email service connection failed');
      }

      // Test customer email service
      tests.customerEmailService = await this.customerNotificationService.testEmailConnection();
      if (!tests.customerEmailService) {
        errors.push('Customer email service connection failed');
      }

      const allTestsPassed = Object.values(tests).every(test => test);

      logger.info('Workflow test completed', {
        success: allTestsPassed,
        tests,
        errorCount: errors.length
      });

      return {
        success: allTestsPassed,
        tests,
        errors
      };
    } catch (error: any) {
      logger.error('Workflow test failed', { error: error.message });
      return {
        success: false,
        tests,
        errors: [error.message]
      };
    }
  }
}
