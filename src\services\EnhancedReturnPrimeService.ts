import nodemailer from 'nodemailer';
import { ReturnPrimeWebhookPayload, TransformedReturnPayload, ReturnItem } from '../types';
import { ShopifyService } from './ShopifyService';
import { VendorDatabaseService, VendorInfo } from './VendorDatabaseService';
import { SupplierNotificationService, SupplierNotificationData } from './SupplierNotificationService';
import { CustomerNotificationService, CustomerNotificationData } from './CustomerNotificationService';
import { transformReturnPrimePayload, getEventTypeFromHeaders } from '../utils/transform';
import { config } from '../config';
import logger from '../utils/logger';

export interface ProcessedReturn {
  returnId: string;
  orderId: string;
  customerEmail: string;
  customerName?: string;
  items: Array<{
    item: ReturnItem;
    vendor?: string;
    vendorInfo?: VendorInfo;
    notificationSent: boolean;
    error?: string;
  }>;
  totalItems: number;
  processedItems: number;
  notificationsSent: number;
  customerNotified: boolean;
  errors: string[];
}

export class EnhancedReturnPrimeService {
  private shopifyService: ShopifyService;
  private vendorDatabase: VendorDatabaseService;
  private notificationService: SupplierNotificationService;
  private customerNotificationService: CustomerNotificationService;
  private transporter: nodemailer.Transporter;

  constructor() {
    this.shopifyService = new ShopifyService();
    this.vendorDatabase = new VendorDatabaseService();
    this.notificationService = new SupplierNotificationService();
    this.customerNotificationService = new CustomerNotificationService();

    // Initialize email transporter
    this.transporter = nodemailer.createTransport({
      host: config.email.host,
      port: config.email.port,
      secure: config.email.secure,
      auth: {
        user: config.email.user,
        pass: config.email.pass
      }
    });
  }

  /**
   * Process Return Prime webhook for refund requests
   */
  public async processRefundWebhook(
    payload: ReturnPrimeWebhookPayload,
    headers: Record<string, any>
  ): Promise<{
    success: boolean;
    processedReturn?: ProcessedReturn;
    error?: string;
  }> {
    try {
      // Transform payload to internal format
      const transformedPayload = transformReturnPrimePayload(payload);
      const eventType = getEventTypeFromHeaders(headers);

      logger.info('Processing Return Prime refund webhook', {
        returnId: transformedPayload.return_id,
        eventType,
        itemCount: transformedPayload.items.length
      });

      // Only process refund events
      if (!eventType.includes('refund')) {
        logger.info('Skipping non-refund event', {
          returnId: transformedPayload.return_id,
          eventType
        });
        return {
          success: true,
          processedReturn: {
            returnId: transformedPayload.return_id,
            orderId: transformedPayload.order_id,
            customerEmail: transformedPayload.customer_email,
            customerName: transformedPayload.customer_name,
            items: [],
            totalItems: 0,
            processedItems: 0,
            notificationsSent: 0,
            customerNotified: false,
            errors: ['Skipped: Not a refund event']
          }
        };
      }

      // Process the return
      const processedReturn = await this.processReturn(transformedPayload);

      return {
        success: true,
        processedReturn
      };
    } catch (error: any) {
      logger.error('Failed to process Return Prime refund webhook', {
        error: error.message,
        payload: JSON.stringify(payload, null, 2)
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process a return request and notify suppliers
   */
  private async processReturn(payload: TransformedReturnPayload): Promise<ProcessedReturn> {
    const processedReturn: ProcessedReturn = {
      returnId: payload.return_id,
      orderId: payload.order_id,
      customerEmail: payload.customer_email,
      customerName: payload.customer_name,
      items: [],
      totalItems: payload.items.length,
      processedItems: 0,
      notificationsSent: 0,
      customerNotified: false,
      errors: []
    };

    try {
      // Process each item using webhook data directly (no API calls needed)
      for (const item of payload.items) {
        const processedItem = {
          item,
          vendor: undefined as string | undefined,
          vendorInfo: undefined as VendorInfo | undefined,
          notificationSent: false,
          error: undefined as string | undefined
        };

        try {
          // Extract vendor from webhook data or use SKU-based mapping
          let vendorName: string | undefined;

          // Try to get vendor from item data if available
          // For now, we'll use SKU-based mapping since webhook doesn't include vendor directly
          vendorName = this.extractVendorFromSKU(item.sku);

          if (!vendorName) {
            // Try mapping based on product title or other identifiers
            vendorName = this.extractVendorFromProductTitle(item.name);
          }

          if (vendorName) {
            // Get vendor info from our database
            const vendorInfo = await this.vendorDatabase.getVendor(vendorName);

            if (vendorInfo) {
              processedItem.vendor = vendorName;
              processedItem.vendorInfo = vendorInfo;

              logger.debug('Found vendor for item from webhook data', {
                sku: item.sku,
                vendor: vendorName,
                hasVendorInfo: !!vendorInfo
              });
            } else {
              processedItem.error = `Vendor '${vendorName}' not found in database`;
              logger.warn('Vendor not found in database', {
                sku: item.sku,
                vendor: vendorName
              });
            }
          } else {
            processedItem.error = 'Could not determine vendor from webhook data';
            logger.warn('No vendor found for item', {
              sku: item.sku,
              productName: item.name
            });
          }

          processedReturn.processedItems++;
        } catch (error: any) {
          processedItem.error = `Failed to process vendor: ${error.message}`;
          logger.error('Error processing item vendor', {
            sku: item.sku,
            error: error.message
          });
        }

        processedReturn.items.push(processedItem);
      }

      // Group items by vendor for notification
      const vendorGroups = this.groupItemsByVendor(processedReturn.items);

      // Process notifications for each vendor
      for (const [vendorName, vendorData] of Object.entries(vendorGroups)) {
        try {
          if (!vendorData.vendorInfo) {
            logger.warn('No vendor info available for notification', {
              vendor: vendorName,
              itemCount: vendorData.items.length
            });

            // Mark items as failed
            vendorData.items.forEach(processedItem => {
              processedItem.error = 'No vendor contact information available';
            });

            processedReturn.errors.push(`No contact info for vendor: ${vendorName}`);
            continue;
          }

          // Create notification data
          const notificationData: SupplierNotificationData = {
            returnId: payload.return_id,
            orderId: payload.order_id,
            customerEmail: payload.customer_email,
            customerName: payload.customer_name,
            items: vendorData.items.map(pi => pi.item),
            totalRefundAmount: payload.refund_amount,
            returnDate: payload.created_at,
            notes: payload.notes
          };

          // 1. SIMULATE supplier notification (don't send to real companies)
          logger.info('SIMULATING supplier notification (not sent to real company)', {
            vendor: vendorName,
            email: vendorData.vendorInfo.contact.email,
            itemCount: vendorData.items.length,
            returnId: payload.return_id
          });

          // Generate supplier email template for copying to support emails
          const supplierTemplate = this.notificationService.generateEmailTemplate(
            vendorData.vendorInfo,
            notificationData
          );

          // 2. Send supplier copy to support emails
          await this.sendSupplierCopyToSupport(
            vendorData.vendorInfo,
            notificationData,
            supplierTemplate
          );

          // Mark items as notified (simulated)
          vendorData.items.forEach(processedItem => {
            processedItem.notificationSent = true;
          });

          processedReturn.notificationsSent++;

          logger.info('Supplier notification simulated and copies sent to support', {
            vendor: vendorName,
            email: vendorData.vendorInfo.contact.email,
            itemCount: vendorData.items.length
          });

        } catch (error: any) {
          vendorData.items.forEach(processedItem => {
            processedItem.error = `Notification error: ${error.message}`;
          });

          processedReturn.errors.push(`Error processing ${vendorName}: ${error.message}`);

          logger.error('Error processing vendor notification', {
            vendor: vendorName,
            error: error.message
          });
        }
      }

      // Send customer notification after processing suppliers
      try {
        const customerNotificationData: CustomerNotificationData = {
          returnId: payload.return_id,
          orderId: payload.order_id,
          orderNumber: payload.order_number,
          customerEmail: payload.customer_email,
          customerName: payload.customer_name,
          items: payload.items,
          requestType: 'return', // Default to return, could be enhanced to detect type
          submittedDate: payload.created_at,
          estimatedProcessingTime: '3-5 business days',
          notes: payload.notes
        };

        // 3. Send customer notification to actual customer
        const customerNotificationResult = await this.customerNotificationService.notifyCustomer(
          customerNotificationData
        );

        if (customerNotificationResult.success) {
          processedReturn.customerNotified = true;
          logger.info('Customer notification sent successfully', {
            customerEmail: payload.customer_email,
            returnId: payload.return_id,
            messageId: customerNotificationResult.messageId
          });
        } else {
          processedReturn.errors.push(`Failed to notify customer: ${customerNotificationResult.error}`);
          logger.error('Failed to send customer notification', {
            customerEmail: payload.customer_email,
            returnId: payload.return_id,
            error: customerNotificationResult.error
          });
        }

        // 4. Send customer copy to support emails
        await this.sendCustomerCopyToSupport(customerNotificationData);

      } catch (error: any) {
        processedReturn.errors.push(`Customer notification error: ${error.message}`);
        logger.error('Error sending customer notification', {
          customerEmail: payload.customer_email,
          returnId: payload.return_id,
          error: error.message
        });
      }

      logger.info('Return processing completed', {
        returnId: payload.return_id,
        totalItems: processedReturn.totalItems,
        processedItems: processedReturn.processedItems,
        notificationsSent: processedReturn.notificationsSent,
        customerNotified: processedReturn.customerNotified,
        errors: processedReturn.errors.length
      });

      return processedReturn;
    } catch (error: any) {
      logger.error('Error processing return', {
        returnId: payload.return_id,
        error: error.message
      });

      processedReturn.errors.push(`Processing error: ${error.message}`);
      return processedReturn;
    }
  }

  /**
   * Group processed items by vendor
   */
  private groupItemsByVendor(
    processedItems: ProcessedReturn['items']
  ): Record<string, {
    vendorInfo?: VendorInfo;
    items: ProcessedReturn['items'];
  }> {
    const groups: Record<string, {
      vendorInfo?: VendorInfo;
      items: ProcessedReturn['items'];
    }> = {};

    for (const processedItem of processedItems) {
      if (processedItem.vendor) {
        if (!groups[processedItem.vendor]) {
          groups[processedItem.vendor] = {
            vendorInfo: processedItem.vendorInfo,
            items: []
          };
        }
        groups[processedItem.vendor].items.push(processedItem);
      }
    }

    return groups;
  }

  /**
   * Get return processing statistics
   */
  public async getProcessingStats(dateFrom?: string, dateTo?: string): Promise<{
    totalReturns: number;
    processedReturns: number;
    notificationsSent: number;
    topVendors: Array<{ vendor: string; returnCount: number }>;
  }> {
    // This would typically query a database of processed returns
    // For now, return basic stats from vendor database
    try {
      const stats = await this.vendorDatabase.getDatabaseStats();
      
      return {
        totalReturns: 0, // Would come from return processing logs
        processedReturns: 0, // Would come from return processing logs
        notificationsSent: 0, // Would come from notification logs
        topVendors: [] // Would come from return processing logs
      };
    } catch (error: any) {
      logger.error('Failed to get processing stats', { error: error.message });
      return {
        totalReturns: 0,
        processedReturns: 0,
        notificationsSent: 0,
        topVendors: []
      };
    }
  }

  /**
   * Test the complete workflow
   */
  public async testWorkflow(): Promise<{
    success: boolean;
    tests: Record<string, boolean>;
    errors: string[];
  }> {
    const tests: Record<string, boolean> = {};
    const errors: string[] = [];

    try {
      // Test Shopify connection
      tests.shopifyConnection = await this.shopifyService.testConnection();
      if (!tests.shopifyConnection) {
        errors.push('Shopify API connection failed');
      }

      // Test vendor database
      try {
        const stats = await this.vendorDatabase.getDatabaseStats();
        tests.vendorDatabase = stats.totalVendors > 0;
        if (!tests.vendorDatabase) {
          errors.push('Vendor database is empty');
        }
      } catch (error: any) {
        tests.vendorDatabase = false;
        errors.push(`Vendor database error: ${error.message}`);
      }

      // Test supplier email service
      tests.supplierEmailService = await this.notificationService.testEmailConnection();
      if (!tests.supplierEmailService) {
        errors.push('Supplier email service connection failed');
      }

      // Test customer email service
      tests.customerEmailService = await this.customerNotificationService.testEmailConnection();
      if (!tests.customerEmailService) {
        errors.push('Customer email service connection failed');
      }

      const allTestsPassed = Object.values(tests).every(test => test);

      logger.info('Workflow test completed', {
        success: allTestsPassed,
        tests,
        errorCount: errors.length
      });

      return {
        success: allTestsPassed,
        tests,
        errors
      };
    } catch (error: any) {
      logger.error('Workflow test failed', { error: error.message });
      return {
        success: false,
        tests,
        errors: [error.message]
      };
    }
  }

  /**
   * Send supplier notification copy to support emails
   */
  private async sendSupplierCopyToSupport(
    vendorInfo: VendorInfo,
    notificationData: SupplierNotificationData,
    supplierTemplate: { subject: string; html: string; text: string }
  ): Promise<void> {
    const supportEmails = ['<EMAIL>', '<EMAIL>'];

    for (const supportEmail of supportEmails) {
      try {
        const mailOptions = {
          from: config.email.from,
          to: supportEmail,
          subject: `[SUPPLIER COPY] ${supplierTemplate.subject}`,
          html: `
            <div style="background-color: #f0f8ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="color: #0066cc; margin: 0;">📋 SUPPLIER NOTIFICATION COPY</h3>
              <p style="margin: 10px 0 0 0; color: #666;">
                This is a copy of the notification that would be sent to: <strong>${vendorInfo.contact.email}</strong>
                <br><strong>Note:</strong> Actual supplier notification was SIMULATED (not sent to real company)
              </p>
            </div>
            ${supplierTemplate.html}
          `,
          text: `
[SUPPLIER COPY] - This is a copy of the notification that would be sent to: ${vendorInfo.contact.email}
Note: Actual supplier notification was SIMULATED (not sent to real company)

${supplierTemplate.text}
          `,
          replyTo: config.email.from
        };

        await this.transporter.sendMail(mailOptions);

        logger.info('Supplier copy sent to support', {
          supportEmail,
          vendor: vendorInfo.name,
          returnId: notificationData.returnId
        });
      } catch (error: any) {
        logger.error('Failed to send supplier copy to support', {
          supportEmail,
          vendor: vendorInfo.name,
          returnId: notificationData.returnId,
          error: error.message
        });
      }
    }
  }

  /**
   * Send customer notification copy to support emails
   */
  private async sendCustomerCopyToSupport(
    customerNotificationData: CustomerNotificationData
  ): Promise<void> {
    const supportEmails = ['<EMAIL>', '<EMAIL>'];

    // Generate customer email template
    const customerTemplate = this.customerNotificationService.generateCustomerEmailTemplate(
      customerNotificationData
    );

    for (const supportEmail of supportEmails) {
      try {
        const mailOptions = {
          from: config.email.from,
          to: supportEmail,
          subject: `[CUSTOMER COPY] ${customerTemplate.subject}`,
          html: `
            <div style="background-color: #f0fff0; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="color: #009900; margin: 0;">👤 CUSTOMER NOTIFICATION COPY</h3>
              <p style="margin: 10px 0 0 0; color: #666;">
                This is a copy of the notification sent to customer: <strong>${customerNotificationData.customerEmail}</strong>
              </p>
            </div>
            ${customerTemplate.html}
          `,
          text: `
[CUSTOMER COPY] - This is a copy of the notification sent to customer: ${customerNotificationData.customerEmail}

${customerTemplate.text}
          `,
          replyTo: config.email.from
        };

        await this.transporter.sendMail(mailOptions);

        logger.info('Customer copy sent to support', {
          supportEmail,
          customerEmail: customerNotificationData.customerEmail,
          returnId: customerNotificationData.returnId
        });
      } catch (error: any) {
        logger.error('Failed to send customer copy to support', {
          supportEmail,
          customerEmail: customerNotificationData.customerEmail,
          returnId: customerNotificationData.returnId,
          error: error.message
        });
      }
    }
  }

  /**
   * Extract vendor name from SKU using common patterns
   */
  private extractVendorFromSKU(sku: string): string | undefined {
    // Common SKU patterns for different vendors
    const skuPatterns: Record<string, string> = {
      'BDDF_': 'GRAV®',
      'SD_': 'SmokeDrop',
      'SMOKE_': 'Smoke Drop',
      'CR_': 'Canna River',
      'CANNA_': 'Canna River',
      'DS_': 'Discreet Smoker',
      'VESSEL_': 'Vessel',
      'BUDDIFY_': 'Buddify'
    };

    for (const [prefix, vendor] of Object.entries(skuPatterns)) {
      if (sku.startsWith(prefix)) {
        return vendor;
      }
    }

    return undefined;
  }

  /**
   * Extract vendor name from product title using common patterns
   */
  private extractVendorFromProductTitle(productName: string): string | undefined {
    const titlePatterns: Record<string, string> = {
      'grav': 'GRAV®',
      'grav labs': 'GRAV®',
      'smoke drop': 'SmokeDrop',
      'canna river': 'Canna River',
      'discreet smoker': 'Discreet Smoker',
      'vessel': 'Vessel',
      'buddify': 'Buddify'
    };

    const lowerTitle = productName.toLowerCase();

    for (const [pattern, vendor] of Object.entries(titlePatterns)) {
      if (lowerTitle.includes(pattern)) {
        return vendor;
      }
    }

    return undefined;
  }
}
