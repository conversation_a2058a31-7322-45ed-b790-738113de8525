import { createAdminApiClient } from '@shopify/admin-api-client';
import axios, { AxiosInstance } from 'axios';
import { ReturnItem, ShopifyReturnRequest } from '../types';
import { config } from '../config';
import logger from '../utils/logger';
import { VendorDatabaseService, VendorInfo } from './VendorDatabaseService';

export class ShopifyService {
  private client: any;
  private restClient: AxiosInstance;
  private vendorDatabase: VendorDatabaseService;

  constructor() {
    this.client = createAdminApiClient({
      storeDomain: config.shopify.storeUrl,
      accessToken: config.shopify.accessToken,
      apiVersion: config.shopify.apiVersion
    });

    // REST API client for more efficient vendor lookups
    this.restClient = axios.create({
      baseURL: `https://${config.shopify.storeUrl}/admin/api/${config.shopify.apiVersion}`,
      headers: {
        'X-Shopify-Access-Token': config.shopify.accessToken,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    this.vendorDatabase = new VendorDatabaseService();
  }

  /**
   * Create a return in Shopify for approved exchanges
   */
  public async createReturn(returnRequest: ShopifyReturnRequest): Promise<{ success: boolean; returnId?: string; error?: string }> {
    try {
      const mutation = `
        mutation returnCreate($input: ReturnInput!) {
          returnCreate(input: $input) {
            return {
              id
              name
              status
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        input: {
          orderId: `gid://shopify/Order/${returnRequest.orderId}`,
          returnLineItems: returnRequest.lineItems.map(item => ({
            fulfillmentLineItemId: `gid://shopify/FulfillmentLineItem/${item.lineItemId}`,
            quantity: item.quantity,
            returnReason: item.reason,
            returnReasonNote: item.reason
          })),
          notifyCustomer: returnRequest.notifyCustomer,
          note: returnRequest.note
        }
      };

      const response = await this.client.request(mutation, { variables });

      if (response.data?.returnCreate?.userErrors?.length > 0) {
        const errors = response.data.returnCreate.userErrors;
        logger.error('Shopify return creation failed with user errors', {
          orderId: returnRequest.orderId,
          errors
        });
        return {
          success: false,
          error: errors.map((e: any) => e.message).join(', ')
        };
      }

      const returnData = response.data?.returnCreate?.return;
      if (!returnData) {
        logger.error('Shopify return creation failed - no return data', {
          orderId: returnRequest.orderId,
          response: response.data
        });
        return {
          success: false,
          error: 'Failed to create return - no return data received'
        };
      }

      logger.info('Shopify return created successfully', {
        orderId: returnRequest.orderId,
        returnId: returnData.id,
        returnName: returnData.name,
        status: returnData.status
      });

      return {
        success: true,
        returnId: returnData.id
      };

    } catch (error) {
      logger.error('Error creating Shopify return', {
        error,
        orderId: returnRequest.orderId
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get order details from Shopify
   */
  public async getOrderDetails(orderId: string): Promise<{ success: boolean; order?: any; error?: string }> {
    try {
      const query = `
        query getOrder($id: ID!) {
          order(id: $id) {
            id
            name
            email
            createdAt
            totalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            customer {
              id
              firstName
              lastName
              email
            }
            lineItems(first: 250) {
              edges {
                node {
                  id
                  name
                  quantity
                  sku
                  variant {
                    id
                    sku
                  }
                  product {
                    id
                    title
                    vendor
                  }
                }
              }
            }
            fulfillments {
              id
              status
              trackingInfo {
                number
                company
              }
              fulfillmentLineItems(first: 250) {
                edges {
                  node {
                    id
                    lineItem {
                      id
                    }
                    quantity
                  }
                }
              }
            }
          }
        }
      `;

      const variables = {
        id: `gid://shopify/Order/${orderId}`
      };

      const response = await this.client.request(query, { variables });

      if (!response.data?.order) {
        logger.warn('Order not found in Shopify', { orderId });
        return {
          success: false,
          error: 'Order not found'
        };
      }

      logger.debug('Retrieved order details from Shopify', {
        orderId,
        orderName: response.data.order.name
      });

      return {
        success: true,
        order: response.data.order
      };

    } catch (error) {
      logger.error('Error retrieving order from Shopify', {
        error,
        orderId
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Find line item IDs for return items
   */
  public async findLineItemIds(orderId: string, returnItems: ReturnItem[]): Promise<Map<string, string>> {
    const lineItemMap = new Map<string, string>();

    try {
      const orderResult = await this.getOrderDetails(orderId);
      
      if (!orderResult.success || !orderResult.order) {
        logger.error('Could not retrieve order for line item mapping', { orderId });
        return lineItemMap;
      }

      const order = orderResult.order;
      const lineItems = order.lineItems.edges.map((edge: any) => edge.node);

      for (const returnItem of returnItems) {
        // Try to match by SKU first
        let matchedLineItem = lineItems.find((lineItem: any) => 
          lineItem.sku === returnItem.sku || 
          lineItem.variant?.sku === returnItem.sku
        );

        // If no SKU match, try to match by product name
        if (!matchedLineItem) {
          matchedLineItem = lineItems.find((lineItem: any) => 
            lineItem.name.toLowerCase().includes(returnItem.name.toLowerCase()) ||
            lineItem.product?.title.toLowerCase().includes(returnItem.name.toLowerCase())
          );
        }

        if (matchedLineItem) {
          // Find corresponding fulfillment line item
          for (const fulfillment of order.fulfillments) {
            const fulfillmentLineItem = fulfillment.fulfillmentLineItems.edges
              .map((edge: any) => edge.node)
              .find((fli: any) => fli.lineItem.id === matchedLineItem.id);

            if (fulfillmentLineItem) {
              lineItemMap.set(returnItem.sku, fulfillmentLineItem.id.replace('gid://shopify/FulfillmentLineItem/', ''));
              logger.debug('Mapped return item to fulfillment line item', {
                sku: returnItem.sku,
                lineItemId: matchedLineItem.id,
                fulfillmentLineItemId: fulfillmentLineItem.id
              });
              break;
            }
          }
        } else {
          logger.warn('Could not find line item for return item', {
            sku: returnItem.sku,
            name: returnItem.name,
            orderId
          });
        }
      }

    } catch (error) {
      logger.error('Error mapping line item IDs', {
        error,
        orderId,
        returnItems: returnItems.map(i => ({ sku: i.sku, name: i.name }))
      });
    }

    return lineItemMap;
  }

  /**
   * Get product details including vendor information
   */
  public async getProductDetails(productId: string): Promise<{
    success: boolean;
    product?: any;
    vendor?: string;
    tags?: string[];
    metafields?: any[];
    vendor_details?: any;
    error?: string;
  }> {
    try {
      const query = `
        query getProduct($id: ID!) {
          product(id: $id) {
            id
            title
            handle
            description
            vendor
            productType
            tags
            status
            createdAt
            updatedAt
            publishedAt
            totalInventory
            options {
              name
              values
            }
            variants(first: 10) {
              edges {
                node {
                  id
                  title
                  sku
                  price
                  compareAtPrice
                  inventoryQuantity
                  weight
                  weightUnit
                }
              }
            }
            images(first: 5) {
              edges {
                node {
                  url
                  altText
                }
              }
            }
            metafields(first: 20) {
              edges {
                node {
                  namespace
                  key
                  value
                  type
                  description
                }
              }
            }
          }
        }
      `;

      const variables = {
        id: `gid://shopify/Product/${productId}`
      };

      const response = await this.client.request(query, {
        variables
      });

      if (response.errors) {
        logger.error('Shopify GraphQL errors in getProductDetails', {
          errors: response.errors,
          productId
        });
        return {
          success: false,
          error: response.errors.message || 'GraphQL error'
        };
      }

      const product = response.data?.product;
      if (!product) {
        return {
          success: false,
          error: 'Product not found'
        };
      }

      const metafields = product.metafields?.edges?.map((edge: any) => edge.node) || [];
      const variants = product.variants?.edges?.map((edge: any) => edge.node) || [];
      const images = product.images?.edges?.map((edge: any) => edge.node) || [];

      logger.info('Successfully fetched product details from Shopify', {
        productId,
        vendor: product.vendor,
        title: product.title,
        productType: product.productType,
        tagCount: product.tags?.length || 0,
        metafieldCount: metafields.length,
        variantCount: variants.length,
        imageCount: images.length
      });

      return {
        success: true,
        product: {
          id: product.id,
          title: product.title,
          handle: product.handle,
          description: product.description,
          vendor: product.vendor,
          productType: product.productType,
          status: product.status,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
          publishedAt: product.publishedAt,
          totalInventory: product.totalInventory,
          options: product.options || [],
          variants,
          images
        },
        vendor: product.vendor,
        tags: product.tags || [],
        metafields,
        vendor_details: {
          name: product.vendor,
          product_count: 1, // This would need a separate query to get actual count
          product_type: product.productType,
          tags_associated: product.tags?.filter((tag: string) =>
            tag.toLowerCase().includes(product.vendor?.toLowerCase() || '')
          ) || []
        }
      };
    } catch (error: any) {
      logger.error('Failed to get product details from Shopify', {
        error: error.response?.data || error.message,
        productId
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get variant details including product information
   */
  public async getVariantDetails(variantId: string): Promise<{
    success: boolean;
    productId?: string;
    vendor?: string;
    sku?: string;
    error?: string;
  }> {
    try {
      const query = `
        query getVariant($id: ID!) {
          productVariant(id: $id) {
            id
            sku
            product {
              id
              vendor
            }
          }
        }
      `;

      const variables = {
        id: `gid://shopify/ProductVariant/${variantId}`
      };

      const response = await this.client.request(query, {
        variables
      });

      if (response.errors) {
        logger.error('Shopify GraphQL errors in getVariantDetails', {
          errors: response.errors,
          variantId
        });
        return {
          success: false,
          error: response.errors.message || 'GraphQL error'
        };
      }

      const variant = response.data?.productVariant;
      if (!variant) {
        return {
          success: false,
          error: 'Variant not found'
        };
      }

      const productId = variant.product?.id?.replace('gid://shopify/Product/', '');

      logger.info('Successfully fetched variant details from Shopify', {
        variantId,
        productId,
        vendor: variant.product?.vendor,
        sku: variant.sku
      });

      return {
        success: true,
        productId,
        vendor: variant.product?.vendor,
        sku: variant.sku
      };
    } catch (error: any) {
      logger.error('Failed to get variant details from Shopify', {
        error: error.response?.data || error.message,
        variantId
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Process exchange for approved return items
   */
  public async processExchange(
    orderId: string,
    returnItems: ReturnItem[],
    customerEmail: string,
    returnId: string
  ): Promise<{ success: boolean; shopifyReturnId?: string; error?: string }> {
    try {
      // Get line item mappings
      const lineItemMap = await this.findLineItemIds(orderId, returnItems);

      if (lineItemMap.size === 0) {
        logger.error('No line items could be mapped for exchange', {
          orderId,
          returnId,
          returnItems: returnItems.map(i => ({ sku: i.sku, name: i.name }))
        });
        return {
          success: false,
          error: 'Could not map return items to order line items'
        };
      }

      // Create return request
      const returnRequest: ShopifyReturnRequest = {
        orderId,
        lineItems: returnItems
          .filter(item => lineItemMap.has(item.sku))
          .map(item => ({
            lineItemId: lineItemMap.get(item.sku)!,
            quantity: item.qty,
            reason: item.reason
          })),
        notifyCustomer: true,
        note: `Return processed via automation system. Return ID: ${returnId}`
      };

      // Create the return in Shopify
      const result = await this.createReturn(returnRequest);

      if (result.success) {
        logger.info('Exchange processed successfully in Shopify', {
          orderId,
          returnId,
          shopifyReturnId: result.returnId,
          processedItems: returnRequest.lineItems.length
        });
      }

      return result;

    } catch (error) {
      logger.error('Error processing exchange in Shopify', {
        error,
        orderId,
        returnId
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test Shopify API connection
   */
  public async testConnection(): Promise<boolean> {
    try {
      const query = `
        query {
          shop {
            name
            email
            domain
          }
        }
      `;

      const response = await this.client.request(query);

      if (response.data?.shop) {
        logger.info('Shopify API connection test successful', {
          shopName: response.data.shop.name,
          domain: response.data.shop.domain
        });
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Shopify API connection test failed', { error });
      return false;
    }
  }

  /**
   * Get vendor information for a product using REST API (more efficient)
   */
  public async getVendorForProduct(productId: string): Promise<{
    success: boolean;
    vendor?: string;
    vendorInfo?: VendorInfo;
    error?: string;
  }> {
    try {
      // First try to get from REST API
      const response = await this.restClient.get(`/products/${productId}.json`);
      const product = response.data?.product;

      if (!product) {
        return {
          success: false,
          error: 'Product not found'
        };
      }

      const vendorName = product.vendor;
      if (!vendorName) {
        return {
          success: false,
          error: 'No vendor specified for product'
        };
      }

      // Get vendor info from database
      const vendorInfo = await this.vendorDatabase.getVendor(vendorName);

      logger.debug('Retrieved vendor for product', {
        productId,
        vendorName,
        hasVendorInfo: !!vendorInfo
      });

      return {
        success: true,
        vendor: vendorName,
        vendorInfo: vendorInfo || undefined
      };
    } catch (error: any) {
      logger.error('Failed to get vendor for product', {
        error: error.response?.data || error.message,
        productId
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get vendor information for a variant using REST API
   */
  public async getVendorForVariant(variantId: string): Promise<{
    success: boolean;
    vendor?: string;
    vendorInfo?: VendorInfo;
    productId?: string;
    error?: string;
  }> {
    try {
      // Get variant info first
      const variantResponse = await this.restClient.get(`/variants/${variantId}.json`);
      const variant = variantResponse.data?.variant;

      if (!variant) {
        return {
          success: false,
          error: 'Variant not found'
        };
      }

      const productId = variant.product_id;

      // Get vendor info for the product
      const vendorResult = await this.getVendorForProduct(productId.toString());

      if (vendorResult.success) {
        return {
          ...vendorResult,
          productId: productId.toString()
        };
      }

      return vendorResult;
    } catch (error: any) {
      logger.error('Failed to get vendor for variant', {
        error: error.response?.data || error.message,
        variantId
      });
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Get vendor information for multiple products in batch
   */
  public async getVendorsForProducts(productIds: string[]): Promise<{
    success: boolean;
    vendors: Record<string, { vendor: string; vendorInfo?: VendorInfo }>;
    errors?: string[];
  }> {
    const vendors: Record<string, { vendor: string; vendorInfo?: VendorInfo }> = {};
    const errors: string[] = [];

    try {
      // Process in batches to avoid rate limits
      const batchSize = 5;
      for (let i = 0; i < productIds.length; i += batchSize) {
        const batch = productIds.slice(i, i + batchSize);

        const batchPromises = batch.map(async (productId) => {
          const result = await this.getVendorForProduct(productId);
          if (result.success && result.vendor) {
            vendors[productId] = {
              vendor: result.vendor,
              vendorInfo: result.vendorInfo
            };
          } else {
            errors.push(`Product ${productId}: ${result.error}`);
          }
        });

        await Promise.all(batchPromises);

        // Small delay between batches to respect rate limits
        if (i + batchSize < productIds.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      logger.info('Batch vendor lookup completed', {
        totalProducts: productIds.length,
        successfulLookups: Object.keys(vendors).length,
        errors: errors.length
      });

      return {
        success: true,
        vendors,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error: any) {
      logger.error('Failed to get vendors for products batch', {
        error: error.message,
        productIds
      });
      return {
        success: false,
        vendors: {},
        errors: [error.message]
      };
    }
  }

  /**
   * Update vendor database with information from Shopify products
   */
  public async syncVendorDatabase(): Promise<{
    success: boolean;
    syncedVendors: number;
    errors?: string[];
  }> {
    try {
      logger.info('Starting vendor database sync from Shopify');

      // Get all unique vendors from Shopify
      const query = `
        query {
          products(first: 250) {
            edges {
              node {
                id
                vendor
                tags
                productType
              }
            }
          }
        }
      `;

      const response = await this.client.request(query);
      const products = response.data?.products?.edges || [];

      // Collect unique vendors
      const vendorStats: Record<string, {
        productCount: number;
        categories: Set<string>;
        tags: Set<string>;
      }> = {};

      for (const productEdge of products) {
        const product = productEdge.node;
        if (product.vendor) {
          if (!vendorStats[product.vendor]) {
            vendorStats[product.vendor] = {
              productCount: 0,
              categories: new Set(),
              tags: new Set()
            };
          }

          vendorStats[product.vendor].productCount++;
          if (product.productType) {
            vendorStats[product.vendor].categories.add(product.productType);
          }
          if (product.tags) {
            product.tags.forEach((tag: string) => vendorStats[product.vendor].tags.add(tag));
          }
        }
      }

      // Update vendor database with discovered vendors
      let syncedCount = 0;
      const errors: string[] = [];

      for (const [vendorName, stats] of Object.entries(vendorStats)) {
        try {
          // Check if vendor already exists
          const existingVendor = await this.vendorDatabase.getVendor(vendorName);

          if (!existingVendor) {
            // Create new vendor entry with basic info
            const newVendor: VendorInfo = {
              name: vendorName,
              priority: 'MEDIUM',
              verified: false,
              contact: {
                email: `contact@${vendorName.toLowerCase().replace(/[^a-z0-9]/g, '')}.com`,
                phone: 'Contact needed',
                website: 'Contact needed',
                address: {
                  street: 'Address needed',
                  city: 'City needed',
                  state: 'State needed',
                  zip: 'ZIP needed',
                  country: 'USA'
                },
                businessHours: 'Contact needed',
                timezone: 'Unknown'
              },
              returnPolicy: {
                url: 'Contact needed',
                timeLimit: 'Contact needed',
                requirements: 'Contact needed',
                restockingFee: 'Contact needed',
                returnAddress: 'Contact needed'
              },
              products: {
                count: stats.productCount,
                categories: Array.from(stats.categories),
                tags: Array.from(stats.tags)
              },
              automation: {
                emailTemplate: `${vendorName.toLowerCase().replace(/[^a-z0-9]/g, '-')}-return`,
                requiresApproval: true,
                autoRefund: false,
                processingTime: 'Contact needed'
              },
              notes: `${vendorName} products supplier - contact information needs verification`
            };

            await this.vendorDatabase.upsertVendor(vendorName, newVendor);
            syncedCount++;
          } else {
            // Update product stats for existing vendor
            existingVendor.products.count = stats.productCount;
            existingVendor.products.categories = Array.from(stats.categories);
            existingVendor.products.tags = Array.from(stats.tags);

            await this.vendorDatabase.upsertVendor(vendorName, existingVendor);
            syncedCount++;
          }
        } catch (error: any) {
          errors.push(`Failed to sync vendor ${vendorName}: ${error.message}`);
        }
      }

      logger.info('Vendor database sync completed', {
        totalVendors: Object.keys(vendorStats).length,
        syncedVendors: syncedCount,
        errors: errors.length
      });

      return {
        success: true,
        syncedVendors: syncedCount,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error: any) {
      logger.error('Failed to sync vendor database', {
        error: error.message
      });
      return {
        success: false,
        syncedVendors: 0,
        errors: [error.message]
      };
    }
  }
}
