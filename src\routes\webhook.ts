import { Router, Request, Response } from 'express';
import { ReturnPrimeWebhookPayload, WebhookResponse } from '../types';
import { captureRawBody, verifySignature, validatePayload } from '../middleware/webhook';
import { ReturnProcessor } from '../services/ReturnProcessor';
import { ReturnPrimeService } from '../services/ReturnPrimeService';
import { generateIdempotencyKey } from '../utils/security';
import { transformReturnPrimePayload, getEventTypeFromHeaders } from '../utils/transform';
import logger from '../utils/logger';

const router = Router();
const returnProcessor = new ReturnProcessor();
const returnPrimeService = new ReturnPrimeService();

/**
 * Health check endpoint
 */
router.get('/health', (_req: Request, res: Response) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

/**
 * Return Prime API test endpoint
 */
router.get('/test/return-prime', async (_req: Request, res: Response) => {
  try {
    const isConnected = await returnPrimeService.testConnection();
    res.json({
      service: 'Return Prime API',
      connected: isConnected,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Return Prime API test failed', { error });
    res.status(500).json({
      service: 'Return Prime API',
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Enhanced workflow test endpoint
 */
router.get('/test/enhanced-workflow', async (_req: Request, res: Response) => {
  try {
    const testResult = await returnProcessor.testEnhancedWorkflow();
    res.json({
      service: 'Enhanced Return Processing Workflow',
      ...testResult,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Enhanced workflow test failed', { error });
    res.status(500).json({
      service: 'Enhanced Return Processing Workflow',
      success: false,
      tests: {},
      errors: [error instanceof Error ? error.message : 'Unknown error'],
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get return details from Return Prime API
 */
router.get('/return-prime/return/:returnId', async (req: Request, res: Response) => {
  try {
    const { returnId } = req.params;
    const result = await returnPrimeService.getReturn(returnId);

    if (result.success) {
      res.json(result.data);
    } else {
      res.status(404).json({
        error: result.error || 'Return not found',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    logger.error('Error fetching return from Return Prime API', { error, returnId: req.params.returnId });
    res.status(500).json({
      error: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get order details from Return Prime API
 */
router.get('/return-prime/order/:orderNumber', async (req: Request, res: Response) => {
  try {
    const { orderNumber } = req.params;
    const result = await returnPrimeService.getOrder(orderNumber);

    if (result.success) {
      res.json(result.data);
    } else {
      res.status(404).json({
        error: result.error || 'Order not found',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    logger.error('Error fetching order from Return Prime API', { error, orderNumber: req.params.orderNumber });
    res.status(500).json({
      error: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Simple logging endpoint for Return Prime webhooks (for debugging)
 */
router.post('/webhook/return-prime-log',
  captureRawBody,
  async (req: Request, res: Response) => {
    try {
      let payload;

      // Parse JSON if body is string
      if (typeof req.body === 'string') {
        try {
          payload = JSON.parse(req.body);
        } catch (parseError) {
          payload = req.body;
        }
      } else {
        payload = req.body;
      }

      // Log everything for debugging
      logger.info('=== RETURN PRIME WEBHOOK DATA ===', {
        headers: req.headers,
        payload: payload,
        rawBody: req.rawBody?.toString(),
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Pretty print the JSON to console for easy reading
      console.log('\n=== RETURN PRIME WEBHOOK JSON ===');
      console.log('Headers:', JSON.stringify(req.headers, null, 2));
      console.log('Payload:', JSON.stringify(payload, null, 2));
      console.log('=== END WEBHOOK DATA ===\n');

      // Always respond with success
      res.json({
        success: true,
        message: 'Webhook data logged successfully',
        timestamp: new Date().toISOString(),
        receivedHeaders: Object.keys(req.headers),
        payloadType: typeof payload,
        payloadSize: req.rawBody?.length || 0
      });

    } catch (error) {
      logger.error('Error logging webhook data', { error });
      res.status(500).json({
        success: false,
        error: 'Failed to log webhook data',
        timestamp: new Date().toISOString()
      });
    }
  }
);

/**
 * Main webhook endpoint for Return Prime events
 */
router.post('/webhook/return-prime',
  captureRawBody,
  // Temporarily disable signature verification for testing
  // verifySignature,
  validatePayload,
  async (req: Request, res: Response) => {
    const rawPayload: ReturnPrimeWebhookPayload = req.body;

    // Transform Return Prime format to our internal format
    const payload = transformReturnPrimePayload(rawPayload);
    const eventType = getEventTypeFromHeaders(req.headers);

    const idempotencyKey = generateIdempotencyKey(
      payload.return_id,
      eventType,
      payload.created_at
    );

    logger.info('Received Return Prime webhook', {
      returnId: payload.return_id,
      eventType: eventType,
      orderId: payload.order_id,
      itemCount: payload.items.length,
      idempotencyKey,
      originalStatus: rawPayload.request.status
    });
    
    try {
      // Check if we've already processed this webhook (idempotency)
      const alreadyProcessed = await returnProcessor.checkIdempotency(idempotencyKey);
      
      if (alreadyProcessed) {
        logger.info('Webhook already processed, skipping', {
          returnId: payload.return_id,
          idempotencyKey
        });

        const response: WebhookResponse = {
          success: true,
          message: 'Webhook already processed'
        };

        res.json(response);
        return;
      }

      // Check if this is a refund event and use enhanced processing
      if (eventType.includes('refund')) {
        logger.info('Processing refund webhook with enhanced service', {
          returnId: payload.return_id,
          eventType,
          idempotencyKey
        });

        const result = await returnProcessor.processRefundWebhook(rawPayload, req.headers, idempotencyKey);

        if (result.success) {
          logger.info('Refund processed successfully', {
            returnId: payload.return_id,
            processedItems: result.processedItems,
            idempotencyKey
          });

          res.json(result);
        } else {
          logger.error('Failed to process refund', {
            returnId: payload.return_id,
            errors: result.errors,
            idempotencyKey
          });

          res.status(500).json(result);
        }
      } else {
        // Use original processing for non-refund events
        const result = await returnProcessor.processReturn(payload, idempotencyKey);

        if (result.success) {
          logger.info('Return processed successfully', {
            returnId: payload.return_id,
            processedItems: result.processedItems,
            idempotencyKey
          });

          res.json(result);
        } else {
          logger.error('Failed to process return', {
            returnId: payload.return_id,
            errors: result.errors,
            idempotencyKey
          });

          res.status(500).json(result);
        }
      }
      
    } catch (error) {
      logger.error('Unexpected error processing webhook', {
        error,
        returnId: payload.return_id,
        idempotencyKey
      });

      const response: WebhookResponse = {
        success: false,
        message: 'Internal server error',
        errors: [{
          code: 'INTERNAL_ERROR',
          message: 'An unexpected error occurred',
          retryable: true
        }]
      };

      res.status(500).json(response);
    }
  }
);

/**
 * Raw data capture endpoint (accepts anything)
 */
router.post('/webhook/capture',
  captureRawBody,
  (req: Request, res: Response) => {
    try {
      let payload;

      if (typeof req.body === 'string') {
        try {
          payload = JSON.parse(req.body);
        } catch (parseError) {
          payload = req.body;
        }
      } else {
        payload = req.body;
      }

      console.log('\n🔥 RAW WEBHOOK CAPTURE 🔥');
      console.log('Timestamp:', new Date().toISOString());
      console.log('Method:', req.method);
      console.log('URL:', req.url);
      console.log('IP:', req.ip);
      console.log('User-Agent:', req.get('User-Agent'));
      console.log('\nHeaders:');
      console.log(JSON.stringify(req.headers, null, 2));
      console.log('\nPayload:');
      console.log(JSON.stringify(payload, null, 2));
      console.log('\nRaw Body:');
      console.log(req.rawBody?.toString());
      console.log('🔥 END CAPTURE 🔥\n');

      res.json({
        success: true,
        message: 'Data captured and logged',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error in capture endpoint:', error);
      res.status(500).json({ error: 'Capture failed' });
    }
  }
);

/**
 * Test endpoint for webhook validation (development only)
 */
if (process.env.NODE_ENV !== 'production') {
  router.post('/webhook/test', (req: Request, res: Response) => {
    logger.info('Test webhook received', { body: req.body });
    res.json({ message: 'Test webhook received successfully' });
  });

  /**
   * Debug endpoint to test Return Prime signature verification
   */
  router.post('/webhook/debug', captureRawBody, (req: Request, res: Response) => {
    const signature = req.headers['x-rp-hmac-sha512'] as string;
    const payload = req.rawBody?.toString() || '';

    logger.info('Debug webhook received', {
      headers: req.headers,
      signature,
      payloadLength: payload.length,
      payload: payload.substring(0, 200) + '...' // Log first 200 chars
    });

    // Test signature verification with different methods
    const crypto = require('crypto');
    const secret = process.env.RETURN_PRIME_WEBHOOK_SECRET || 'test_webhook_secret_123';

    // Method 1: SHA512 HMAC
    const hmac512 = crypto.createHmac('sha512', secret).update(payload, 'utf8').digest('hex');

    // Method 2: SHA256 HMAC
    const hmac256 = crypto.createHmac('sha256', secret).update(payload, 'utf8').digest('hex');

    // Method 3: Base64 encoded
    const hmac512Base64 = crypto.createHmac('sha512', secret).update(payload, 'utf8').digest('base64');

    res.json({
      received_signature: signature,
      computed_sha512: hmac512,
      computed_sha256: hmac256,
      computed_sha512_base64: hmac512Base64,
      signature_matches_sha512: signature === hmac512,
      signature_matches_sha256: signature === hmac256,
      signature_matches_sha512_base64: signature === hmac512Base64,
      payload_length: payload.length,
      secret_used: secret
    });
  });
}

export default router;
