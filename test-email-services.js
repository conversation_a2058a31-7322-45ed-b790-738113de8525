require('dotenv').config();
const nodemailer = require('nodemailer');

// Import the actual email services for template generation
const { CustomerNotificationService } = require('./dist/services/CustomerNotificationService');
const { SupplierNotificationService } = require('./dist/services/SupplierNotificationService');

class EmailServiceTest {
  constructor() {
    this.testEmail = '<EMAIL>';

    // Create direct transporter like simple-email-test.js
    this.transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: 587,
      secure: false, // Use TLS
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });

    // Create service instances for template generation only
    this.customerService = new CustomerNotificationService();
    this.supplierService = new SupplierNotificationService();
  }

  /**
   * Test customer email using direct email sending with service templates
   */
  async testCustomerEmail() {
    console.log('📧 Testing Customer Email Service...\n');

    const customerData = {
      returnId: 'TEST-CUSTOMER-' + Date.now(),
      orderId: 'ORDER-12345',
      orderNumber: '#BEAUTIFUL-TEST-001',
      customerEmail: this.testEmail,
      customerName: 'Feranmi Olawore',
      items: [
        {
          name: 'Vessel - Air [Emerald] Premium Vaporizer',
          sku: 'vessel-air-emerald-001',
          qty: 2,
          reason: 'Product arrived damaged - packaging was torn',
          product_id: '9698464760088',
          variant_id: '49924238278936'
        },
        {
          name: 'GRAV® Helix™ Chillum - Clear Glass',
          sku: 'grav-helix-chillum-clear',
          qty: 1,
          reason: 'Wrong size ordered - customer wanted larger version',
          product_id: '9698464760089',
          variant_id: '49924238278937'
        }
      ],
      requestType: 'return',
      submittedDate: new Date().toISOString(),
      estimatedProcessingTime: '3-5 business days',
      trackingInfo: {
        returnLabel: 'Available in customer portal',
        trackingNumber: 'TEST123456789',
        carrierName: 'UPS'
      },
      notes: 'Customer is a VIP member - please prioritize this return request'
    };

    try {
      // Generate email template using the service
      const emailTemplate = this.customerService.generateCustomerEmailTemplate(customerData);

      // Send email directly using our transporter
      const result = await this.transporter.sendMail({
        from: process.env.EMAIL_FROM,
        to: this.testEmail,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
        text: emailTemplate.text
      });

      console.log('✅ Customer email sent successfully!');
      console.log(`📧 Sent to: ${this.testEmail}`);
      console.log(`📨 Message ID: ${result.messageId}`);
      console.log('\n🎨 Customer Email Features:');
      console.log('   • Beautiful blue gradient header');
      console.log('   • White background with professional layout');
      console.log('   • Mobile-responsive design');
      console.log('   • 3D-style status badges and cards');
      console.log('   • Progress timeline with visual indicators');
      console.log('   • Professional Inter font typography');
      return true;
    } catch (error) {
      console.log('❌ Error sending customer email:', error.message);
      return false;
    }
  }

  /**
   * Test supplier email using direct email sending with service templates
   */
  async testSupplierEmail() {
    console.log('📧 Testing Supplier Email Service...\n');

    // Mock vendor info
    const vendorInfo = {
      name: 'Vessel',
      priority: 'HIGH',
      verified: true,
      contact: {
        email: this.testEmail, // Send to test email
        phone: '******-VESSEL',
        website: 'https://vessel.com',
        address: {
          street: '123 Vessel Street',
          city: 'San Francisco',
          state: 'CA',
          zip: '94102',
          country: 'USA'
        },
        businessHours: 'Mon-Fri 9AM-6PM PST',
        timezone: 'PST'
      },
      returnPolicy: {
        url: 'https://vessel.com/returns',
        timeLimit: '30 days',
        requirements: 'Original packaging required, unused condition',
        restockingFee: '15% for opened items',
        returnAddress: '123 Vessel Street, San Francisco, CA 94102'
      },
      products: {
        count: 25,
        categories: ['Vaporizers', 'Accessories'],
        tags: ['premium', 'portable', 'battery']
      },
      automation: {
        emailTemplate: 'vessel-return',
        requiresApproval: true,
        autoRefund: false,
        processingTime: '2-3 business days'
      },
      notes: 'Premium supplier - high priority processing required'
    };

    const supplierData = {
      returnId: 'TEST-SUPPLIER-' + Date.now(),
      orderId: 'ORDER-12345',
      orderNumber: '#BEAUTIFUL-TEST-001',
      customerEmail: '<EMAIL>',
      customerName: 'Feranmi Olawore',
      customerAddress: {
        street: '456 Customer Lane',
        city: 'New York',
        state: 'NY',
        zip: '10001',
        country: 'USA'
      },
      items: [
        {
          name: 'Vessel - Air [Emerald] Premium Vaporizer',
          sku: 'vessel-air-emerald-001',
          qty: 2,
          reason: 'Product arrived damaged - packaging was torn',
          product_id: '9698464760088',
          variant_id: '49924238278936'
        },
        {
          name: 'Vessel - Compass [Black] Portable Vaporizer',
          sku: 'vessel-compass-black-001',
          qty: 1,
          reason: 'Customer changed mind - wants different color',
          product_id: '9698464760090',
          variant_id: '49924238278938'
        }
      ],
      totalRefundAmount: 269.97,
      refundMethod: 'Original payment method',
      returnReason: 'Damaged product and color preference',
      returnDate: new Date().toISOString(),
      notes: 'VIP customer - please handle with priority. Customer mentioned packaging was damaged during shipping.'
    };

    try {
      // Now we can use the actual service template since it's public
      const emailTemplate = this.supplierService.generateEmailTemplate(vendorInfo, supplierData);

      // Send email directly using our transporter
      const result = await this.transporter.sendMail({
        from: process.env.EMAIL_FROM,
        to: this.testEmail,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
        text: emailTemplate.text
      });

      console.log('✅ Supplier email sent successfully!');
      console.log(`📧 Sent to: ${this.testEmail}`);
      console.log(`📨 Message ID: ${result.messageId}`);
      console.log('\n🎨 Supplier Email Features:');
      console.log('   • Professional alert styling');
      console.log('   • White background with clean layout');
      console.log('   • Mobile-responsive design');
      console.log('   • HIGH priority color coding');
      console.log('   • Step-by-step action guides');
      console.log('   • Interactive contact buttons');
      return true;
    } catch (error) {
      console.log('❌ Error sending supplier email:', error.message);
      return false;
    }
  }

  /**
   * Test email connection using direct transporter
   */
  async testEmailConnection() {
    console.log('🔗 Testing email connection...\n');

    try {
      // Test connection directly like simple-email-test.js
      await this.transporter.verify();
      console.log('✅ Email connection successful!');
      return true;
    } catch (error) {
      console.log('❌ Email connection test failed:', error.message);

      if (error.code === 'EAUTH') {
        console.log('\n🔧 Email Authentication Issue:');
        console.log('   • Check Gmail app password is correct');
        console.log('   • Ensure 2-factor authentication is enabled');
        console.log('   • Verify EMAIL_USER and EMAIL_PASS in .env file');
        console.log('   • Make sure "Less secure app access" is disabled (use app passwords)');
      }

      return false;
    }
  }

  /**
   * Run all email tests
   */
  async runAllTests() {
    console.log('🚀 Starting Email Service Tests...\n');
    console.log(`📧 Test Email Address: ${this.testEmail}\n`);
    console.log('='.repeat(60) + '\n');

    // Test connection first
    console.log('TEST 1: Email Connection');
    console.log('-'.repeat(30));
    const connectionOk = await this.testEmailConnection();
    
    if (!connectionOk) {
      console.log('\n❌ Email connection failed. Please check your email configuration.');
      return false;
    }

    console.log('\n' + '='.repeat(60) + '\n');

    // Test customer email
    console.log('TEST 2: Customer Email (Mobile-Responsive)');
    console.log('-'.repeat(30));
    const customerSuccess = await this.testCustomerEmail();

    console.log('\n' + '='.repeat(60) + '\n');

    // Test supplier email
    console.log('TEST 3: Supplier Email (Mobile-Responsive)');
    console.log('-'.repeat(30));
    const supplierSuccess = await this.testSupplierEmail();

    console.log('\n' + '='.repeat(60) + '\n');

    // Summary
    console.log('📊 EMAIL TEST SUMMARY');
    console.log('-'.repeat(30));
    
    const allSuccess = customerSuccess && supplierSuccess;
    
    console.log(`Overall Result: ${allSuccess ? '✅ ALL EMAILS SENT' : '⚠️ SOME EMAILS FAILED'}`);
    console.log(`Customer Email: ${customerSuccess ? '✅ SENT' : '❌ FAILED'}`);
    console.log(`Supplier Email: ${supplierSuccess ? '✅ SENT' : '❌ FAILED'}`);
    
    if (allSuccess) {
      console.log('\n🎉 SUCCESS! Check your <NAME_EMAIL>');
      console.log('\n📧 You should receive:');
      console.log('   1. 🎨 Customer Confirmation Email');
      console.log('      • White background with blue header');
      console.log('      • Mobile-responsive design');
      console.log('      • Professional typography and layout');
      console.log('      • Clean, modern appearance');
      
      console.log('\n   2. 🚨 Supplier Alert Email');
      console.log('      • White background with professional styling');
      console.log('      • Mobile-responsive design');
      console.log('      • Clear action items and contact info');
      console.log('      • Business-friendly appearance');
      
      console.log('\n💡 Fixed Issues:');
      console.log('   • ✅ Removed red background');
      console.log('   • ✅ White background for both emails');
      console.log('   • ✅ Mobile-responsive design');
      console.log('   • ✅ Proper screen fitting');
      console.log('   • ✅ No endless scrolling');
    } else {
      console.log('\n❌ Some emails failed to send');
      console.log('🔧 Please check:');
      console.log('   • Email configuration in .env file');
      console.log('   • SMTP server settings');
      console.log('   • Network connectivity');
      console.log('   • Email service authentication');
    }

    console.log('\n🏁 Email service test completed!');
    return allSuccess;
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new EmailServiceTest();
  tester.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('💥 Email service test failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  });
}

module.exports = EmailServiceTest;
