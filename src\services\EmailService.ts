import nodemailer from 'nodemailer';
import { SupplierNotification, EmailData, EmailTemplate } from '../types';
import { config } from '../config';
import { sanitizeEmailContent } from '../utils/security';
import logger from '../utils/logger';

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = this.initializeTransporter();
  }

  /**
   * Initialize email transporter using SMTP
   */
  private initializeTransporter(): nodemailer.Transporter {
    try {
      const transporter = nodemailer.createTransport({
        host: config.email.host,
        port: config.email.port,
        secure: config.email.secure,
        auth: {
          user: config.email.user,
          pass: config.email.pass
        }
      });

      logger.info('Email transporter initialized successfully', {
        host: config.email.host,
        port: config.email.port,
        secure: config.email.secure
      });

      return transporter;
    } catch (error) {
      logger.error('Failed to initialize email transporter', { error });
      throw new Error('Email service initialization failed');
    }
  }

  /**
   * Generate email template for supplier notification
   */
  public generateSupplierEmailTemplate(notification: SupplierNotification): EmailTemplate {
    const { returnId, orderId, customerEmail, customerName, items, createdAt } = notification;
    
    const itemsHtml = items.map(item => `
      <tr>
        <td style="padding: 8px; border: 1px solid #ddd;">${sanitizeEmailContent(item.sku)}</td>
        <td style="padding: 8px; border: 1px solid #ddd;">${sanitizeEmailContent(item.name)}</td>
        <td style="padding: 8px; border: 1px solid #ddd;">${item.qty}</td>
        <td style="padding: 8px; border: 1px solid #ddd;">${sanitizeEmailContent(item.reason)}</td>
        <td style="padding: 8px; border: 1px solid #ddd;">
          ${item.images ? item.images.map(img => `<a href="${img}" target="_blank">View Image</a>`).join('<br>') : 'No images'}
        </td>
      </tr>
    `).join('');

    const subject = `Return Request ${returnId} – ${items.map(i => i.sku).join(', ')}`;
    
    const body = `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 800px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
              Return Request Notification
            </h2>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #2c3e50;">Return Details</h3>
              <p><strong>Return ID:</strong> ${sanitizeEmailContent(returnId)}</p>
              <p><strong>Order ID:</strong> ${sanitizeEmailContent(orderId)}</p>
              <p><strong>Customer:</strong> ${customerName ? sanitizeEmailContent(customerName) : 'N/A'} (${sanitizeEmailContent(customerEmail)})</p>
              <p><strong>Date:</strong> ${new Date(createdAt).toLocaleString()}</p>
            </div>

            <h3 style="color: #2c3e50;">Items to Return</h3>
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
              <thead>
                <tr style="background-color: #3498db; color: white;">
                  <th style="padding: 12px; border: 1px solid #ddd;">SKU</th>
                  <th style="padding: 12px; border: 1px solid #ddd;">Product Name</th>
                  <th style="padding: 12px; border: 1px solid #ddd;">Quantity</th>
                  <th style="padding: 12px; border: 1px solid #ddd;">Reason</th>
                  <th style="padding: 12px; border: 1px solid #ddd;">Images</th>
                </tr>
              </thead>
              <tbody>
                ${itemsHtml}
              </tbody>
            </table>

            ${notification.requiresManualReview ? `
              <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h4 style="color: #856404; margin-top: 0;">⚠️ Manual Review Required</h4>
                <p style="color: #856404; margin-bottom: 0;">
                  This return requires manual review before processing. Please review the items and contact our team if you have any questions.
                </p>
              </div>
            ` : ''}

            <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h4 style="color: #2d5a2d; margin-top: 0;">Next Steps</h4>
              <ul style="color: #2d5a2d;">
                <li>Review the return request details above</li>
                <li>Process the return according to your standard procedures</li>
                <li>Contact the customer directly if additional information is needed</li>
                <li>Update your inventory systems as appropriate</li>
              </ul>
            </div>

            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
            
            <p style="font-size: 12px; color: #666; text-align: center;">
              This is an automated notification from BakeBuds Return Management System.<br>
              If you have questions, please contact our support team.
            </p>
          </div>
        </body>
      </html>
    `;

    return { subject, body };
  }

  /**
   * Send email notification to supplier
   */
  public async sendSupplierNotification(notification: SupplierNotification): Promise<boolean> {
    try {

      const template = this.generateSupplierEmailTemplate(notification);
      
      const emailData: EmailData = {
        to: notification.email,
        subject: template.subject,
        html: template.body
      };

      await this.sendEmail(emailData);
      
      logger.info('Supplier notification sent successfully', {
        supplier: notification.supplier,
        email: notification.email,
        returnId: notification.returnId,
        itemCount: notification.items.length
      });

      return true;
    } catch (error) {
      logger.error('Failed to send supplier notification', {
        error,
        supplier: notification.supplier,
        email: notification.email,
        returnId: notification.returnId
      });
      return false;
    }
  }

  /**
   * Send internal review notification
   */
  public async sendInternalReviewNotification(
    notification: SupplierNotification,
    reason: string = 'Manual review required'
  ): Promise<boolean> {
    try {
      const subject = `Internal Review Required - Return ${notification.returnId}`;
      
      const body = `
        <html>
          <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
              <h2 style="color: #e74c3c;">Internal Review Required</h2>
              
              <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <p><strong>Reason:</strong> ${sanitizeEmailContent(reason)}</p>
                <p><strong>Return ID:</strong> ${sanitizeEmailContent(notification.returnId)}</p>
                <p><strong>Supplier:</strong> ${sanitizeEmailContent(notification.supplier)}</p>
                <p><strong>Customer:</strong> ${sanitizeEmailContent(notification.customerEmail)}</p>
              </div>

              <h3>Items Requiring Review:</h3>
              <ul>
                ${notification.items.map(item => `
                  <li>${sanitizeEmailContent(item.sku)} - ${sanitizeEmailContent(item.name)} (Qty: ${item.qty})</li>
                `).join('')}
              </ul>

              <p>Please review this return request in the admin panel.</p>
            </div>
          </body>
        </html>
      `;

      const emailData: EmailData = {
        to: config.internalReviewEmail,
        subject,
        html: body
      };

      await this.sendEmail(emailData);
      
      logger.info('Internal review notification sent', {
        returnId: notification.returnId,
        supplier: notification.supplier
      });

      return true;
    } catch (error) {
      logger.error('Failed to send internal review notification', {
        error,
        returnId: notification.returnId
      });
      return false;
    }
  }

  /**
   * Send email using the configured transporter
   */
  private async sendEmail(emailData: EmailData): Promise<void> {
    if (!this.transporter) {
      throw new Error('Email transporter not initialized');
    }

    const mailOptions = {
      from: config.email.from,
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html,
      attachments: emailData.attachments
    };

    await this.transporter.sendMail(mailOptions);
  }

  /**
   * Test email configuration
   */
  public async testEmailConfiguration(): Promise<boolean> {
    try {
      await this.transporter.verify();
      logger.info('Email configuration test successful');
      return true;
    } catch (error) {
      logger.error('Email configuration test failed', { error });
      return false;
    }
  }
}
