import nodemailer from 'nodemailer';
import { config } from '../config';
import logger from '../utils/logger';
import { VendorInfo } from './VendorDatabaseService';
import { ReturnItem } from '../types';

export interface SupplierNotificationData {
  returnId: string;
  orderId: string;
  orderNumber?: string;
  customerEmail: string;
  customerName?: string;
  customerAddress?: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  items: ReturnItem[];
  totalRefundAmount?: number;
  refundMethod?: string;
  returnReason?: string;
  returnDate: string;
  notes?: string;
}

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export class SupplierNotificationService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: config.email.host,
      port: config.email.port,
      secure: config.email.secure,
      auth: {
        user: config.email.user,
        pass: config.email.pass
      }
    });
  }

  /**
   * Send return notification to supplier
   */
  public async notifySupplier(
    vendor: VendorInfo,
    notificationData: SupplierNotificationData
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Generate email template
      const template = this.generateEmailTemplate(vendor, notificationData);
      
      // Send email
      const mailOptions = {
        from: config.email.from,
        to: vendor.contact.email,
        subject: template.subject,
        html: template.html,
        text: template.text,
        replyTo: config.email.from
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      logger.info('Supplier notification sent successfully', {
        vendor: vendor.name,
        email: vendor.contact.email,
        returnId: notificationData.returnId,
        messageId: result.messageId
      });

      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error: any) {
      logger.error('Failed to send supplier notification', {
        vendor: vendor.name,
        email: vendor.contact.email,
        returnId: notificationData.returnId,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate beautiful 3D-style email template for supplier notification
   */
  public generateEmailTemplate(vendor: VendorInfo, data: SupplierNotificationData): EmailTemplate {
    const subject = `Return Request #${data.returnId} - ${vendor.name} Products - Action Required`;

    // Generate simple items list
    const itemsHtml = data.items.map(item => `
      <div class="info-card">
        <h4 style="margin: 0 0 8px 0; font-weight: 600; color: #333333;">
          ${item.name}
        </h4>
        <p style="margin: 4px 0; color: #666666;">
          <strong>SKU:</strong> ${item.sku}
        </p>
        <p style="margin: 4px 0; color: #666666;">
          <strong>Quantity:</strong> ${item.qty}
        </p>
        <p style="margin: 4px 0; color: #666666;">
          <strong>Return Reason:</strong> ${item.reason}
        </p>
      </div>
    `).join('');

    const itemsText = data.items.map(item =>
      `- ${item.name} (SKU: ${item.sku}) - Qty: ${item.qty} - Reason: ${item.reason}`
    ).join('\n');

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Return Request Notification - ${vendor.name}</title>
        <style>
          * {
            box-sizing: border-box;
          }

          body {
            margin: 0;
            padding: 20px;
            background: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333333;
          }

          @media (max-width: 600px) {
            body {
              padding: 15px;
            }
          }

          @media (prefers-color-scheme: dark) {
            body {
              background: #000000 !important;
              color: #ffffff !important;
            }
          }

          .container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            padding: 0;
          }

          @media (max-width: 600px) {
            .container {
              margin: 0;
              max-width: 100%;
              border-radius: 4px;
              border: none;
            }
          }

          @media (prefers-color-scheme: dark) {
            .container {
              background: #111111 !important;
              border: 1px solid #333333 !important;
              color: #ffffff !important;
            }
          }

          .header {
            background: #f8f9fa;
            padding: 24px;
            border-bottom: 1px solid #e5e5e5;
            border-radius: 8px 8px 0 0;
          }

          @media (max-width: 600px) {
            .header {
              padding: 20px;
              border-radius: 4px 4px 0 0;
            }
          }

          @media (prefers-color-scheme: dark) {
            .header {
              background: #1a1a1a !important;
              border-bottom: 1px solid #333333 !important;
            }
          }

          .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #333333;
          }

          @media (max-width: 600px) {
            .header h1 {
              font-size: 18px;
            }
          }

          @media (prefers-color-scheme: dark) {
            .header h1 {
              color: #ffffff !important;
            }
          }

          .content {
            padding: 24px;
            background: #ffffff;
          }

          @media (max-width: 600px) {
            .content {
              padding: 20px;
            }
          }

          @media (prefers-color-scheme: dark) {
            .content {
              background: #111111 !important;
              color: #ffffff !important;
            }
          }

          .info-card {
            background: #f8f9fa;
            border: 1px solid #e5e5e5;
            border-radius: 4px;
            padding: 16px;
            margin: 16px 0;
          }

          @media (max-width: 600px) {
            .info-card {
              padding: 14px;
              margin: 12px 0;
            }
          }

          @media (prefers-color-scheme: dark) {
            .info-card {
              background: #1a1a1a !important;
              border: 1px solid #333333 !important;
              color: #ffffff !important;
            }
          }

          h2, h3, h4 {
            margin: 0 0 12px 0;
            color: #333333;
            font-weight: 600;
          }

          @media (prefers-color-scheme: dark) {
            h2, h3, h4 {
              color: #ffffff !important;
            }
          }

          p {
            margin: 0 0 8px 0;
            color: #666666;
            line-height: 1.5;
          }

          @media (prefers-color-scheme: dark) {
            p {
              color: #cccccc !important;
            }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Return Request Alert</h1>
          </div>

          <div class="content">
            <p><strong>Hello ${vendor.name} Team,</strong></p>
            <p>A customer has submitted a return request for your products. Please review and take action promptly.</p>

            <div class="info-card">
              <h3>Return Request Details</h3>
              <p><strong>Return ID:</strong> #${data.returnId}</p>
              <p><strong>Order ID:</strong> #${data.orderId}</p>
              ${data.orderNumber ? `
              <p><strong>Order Number:</strong> #${data.orderNumber}</p>
              ` : ''}
              <p><strong>Return Date:</strong> ${new Date(data.returnDate).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}</p>
              <p><strong>Customer:</strong> ${data.customerName || 'N/A'} (${data.customerEmail})</p>
              ${data.totalRefundAmount ? `
              <p><strong>Refund Amount:</strong> $${data.totalRefundAmount.toFixed(2)}</p>
              ` : ''}
              ${data.refundMethod ? `
              <p><strong>Refund Method:</strong> ${data.refundMethod}</p>
              ` : ''}
            </div>

            <h3>Returned Items (${data.items.length})</h3>
            ${itemsHtml}

            ${data.customerAddress ? `
            <div class="info-card">
              <h3 style="margin-top: 0; color: #2d3748; display: flex; align-items: center;">
                <span style="margin-right: 10px;">📍</span> Customer Address
              </h3>
              <div style="
                background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
                padding: 20px;
                border-radius: 12px;
                border-left: 4px solid #38a169;
                box-shadow: 0 4px 15px rgba(56, 161, 105, 0.1);
              ">
                <p style="margin: 0; color: #2d3748; font-weight: 500; line-height: 1.8;">
                  📍 ${data.customerAddress.street}<br>
                  🏙️ ${data.customerAddress.city}, ${data.customerAddress.state} ${data.customerAddress.zip}<br>
                  🌍 ${data.customerAddress.country}
                </p>
              </div>
            </div>
            ` : ''}

            ${data.notes ? `
            <div class="info-card">
              <h3>Customer Notes</h3>
              <p><em>"${data.notes}"</em></p>
            </div>
            ` : ''}

            <h3>Action Required - Next Steps</h3>
            <p>1. <strong>Review Details:</strong> Carefully examine the return request information above</p>
            <p>2. <strong>Verify Requirements:</strong> Check return reason against your return policy</p>
            <p>3. <strong>Customer Contact:</strong> Reach out if additional information is needed</p>
            <p>4. <strong>Process Return:</strong> Handle according to your established procedures</p>
            <p>5. <strong>Update Systems:</strong> Adjust inventory and internal records</p>

            <div class="info-card">
              <h3>Your Return Policy Reference</h3>
              <p><strong>Time Limit:</strong> ${vendor.returnPolicy.timeLimit}</p>
              <p><strong>Requirements:</strong> ${vendor.returnPolicy.requirements}</p>
              <p><strong>Restocking Fee:</strong> ${vendor.returnPolicy.restockingFee}</p>
              <p><strong>Return Address:</strong> ${vendor.returnPolicy.returnAddress}</p>
              ${vendor.returnPolicy.url !== 'Contact needed' ? `
              <p><strong>Policy URL:</strong> <a href="${vendor.returnPolicy.url}">${vendor.returnPolicy.url}</a></p>
              ` : ''}
            </div>

            <p>For questions, contact the customer at ${data.customerEmail} or our support team at ${config.email.from}.</p>

            <div class="info-card">
              <h3>Processing Information</h3>
              <p><strong>Processing Time:</strong> ${vendor.automation.processingTime}</p>
              <p><strong>Approval Required:</strong> ${vendor.automation.requiresApproval ? 'Yes - This return requires your approval before processing' : 'No - This return can be processed automatically'}</p>
            </div>

            <p>Thank you for being a valued supplier partner. We appreciate your prompt attention to customer returns.</p>

            <hr style="border: none; border-top: 1px solid #e5e5e5; margin: 24px 0;">

            <p style="font-size: 14px; color: #666666;">
              This is an automated notification from Bake Buds Return Processing System.<br>
              Questions? Contact our supplier support team at ${config.email.from}
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
🚨 RETURN REQUEST ALERT - ${vendor.name}
⚡ IMMEDIATE ACTION REQUIRED

Hello ${vendor.name} Team!

A customer has submitted a return request for your products. Please review and take action promptly.

📋 RETURN REQUEST DETAILS (${vendor.priority} PRIORITY):
- Return ID: #${data.returnId}
- Order ID: #${data.orderId}
${data.orderNumber ? `- Order Number: #${data.orderNumber}\n` : ''}
- Return Date: ${new Date(data.returnDate).toLocaleDateString('en-US', {
  weekday: 'long',
  year: 'numeric',
  month: 'long',
  day: 'numeric'
})}
- Customer: ${data.customerName || 'N/A'} (${data.customerEmail})
${data.totalRefundAmount ? `- Refund Amount: $${data.totalRefundAmount.toFixed(2)}\n` : ''}
${data.refundMethod ? `- Refund Method: ${data.refundMethod}\n` : ''}

📦 RETURNED ITEMS (${data.items.length} ITEM${data.items.length > 1 ? 'S' : ''}):
${itemsText}

${data.customerAddress ? `
📍 CUSTOMER ADDRESS:
📍 ${data.customerAddress.street}
🏙️ ${data.customerAddress.city}, ${data.customerAddress.state} ${data.customerAddress.zip}
🌍 ${data.customerAddress.country}
` : ''}

${data.notes ? `
💬 CUSTOMER NOTES:
"${data.notes}"
` : ''}

🎯 ACTION REQUIRED - NEXT STEPS:
1. Review Details: Carefully examine the return request information above
2. Verify Requirements: Check return reason against your return policy
3. Customer Contact: Reach out if additional information is needed
4. Process Return: Handle according to your established procedures
5. Update Systems: Adjust inventory and internal records

📋 YOUR RETURN POLICY REFERENCE:
⏰ Time Limit: ${vendor.returnPolicy.timeLimit}
📝 Requirements: ${vendor.returnPolicy.requirements}
💰 Restocking Fee: ${vendor.returnPolicy.restockingFee}
📮 Return Address: ${vendor.returnPolicy.returnAddress}
${vendor.returnPolicy.url !== 'Contact needed' ? `🔗 Policy URL: ${vendor.returnPolicy.url}\n` : ''}

⚡ PROCESSING TIME: ${vendor.automation.processingTime}
${vendor.automation.requiresApproval ?
  '⚠️ This return requires your approval before processing' :
  '✅ This return can be processed automatically'
}

🤝 BAKE BUDS SUPPLIER PARTNERSHIP
Thank you for being a valued supplier partner. We appreciate your prompt attention to customer returns.

This is an automated notification from Bake Buds Return Processing System.
Questions? Contact our supplier support team at ${config.email.from}

Customer Email: ${data.customerEmail}
Support Email: ${config.email.from}
    `;

    return {
      subject,
      html,
      text
    };
  }

  /**
   * Send batch notifications to multiple suppliers
   */
  public async notifyMultipleSuppliers(
    notifications: Array<{
      vendor: VendorInfo;
      data: SupplierNotificationData;
    }>
  ): Promise<{
    success: boolean;
    results: Array<{
      vendor: string;
      success: boolean;
      messageId?: string;
      error?: string;
    }>;
  }> {
    const results: Array<{
      vendor: string;
      success: boolean;
      messageId?: string;
      error?: string;
    }> = [];

    try {
      // Process notifications in parallel with a reasonable concurrency limit
      const batchSize = 3;
      for (let i = 0; i < notifications.length; i += batchSize) {
        const batch = notifications.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async ({ vendor, data }) => {
          const result = await this.notifySupplier(vendor, data);
          return {
            vendor: vendor.name,
            success: result.success,
            messageId: result.messageId,
            error: result.error
          };
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
        
        // Small delay between batches to avoid overwhelming email server
        if (i + batchSize < notifications.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      const successCount = results.filter(r => r.success).length;
      
      logger.info('Batch supplier notifications completed', {
        total: notifications.length,
        successful: successCount,
        failed: notifications.length - successCount
      });

      return {
        success: true,
        results
      };
    } catch (error: any) {
      logger.error('Failed to send batch supplier notifications', {
        error: error.message,
        totalNotifications: notifications.length
      });

      return {
        success: false,
        results
      };
    }
  }

  /**
   * Test email configuration
   */
  public async testEmailConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      logger.info('Email connection test successful');
      return true;
    } catch (error: any) {
      logger.error('Email connection test failed', { error: error.message });
      return false;
    }
  }
}
