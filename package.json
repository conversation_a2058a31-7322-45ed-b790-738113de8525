{"name": "shopify-return-automation", "version": "1.0.0", "description": "Webhook-based return/warranty system for Shopify with supplier integration", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["shopify", "webhook", "automation", "returns", "warranty"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "googleapis": "^128.0.0", "nodemailer": "^6.9.7", "@shopify/admin-api-client": "^0.2.2", "airtable": "^0.12.2", "crypto": "^1.0.1", "winston": "^3.11.0", "joi": "^17.11.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.9.0", "@types/nodemailer": "^6.4.14", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}}